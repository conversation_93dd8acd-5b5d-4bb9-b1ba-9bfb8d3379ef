/**
 * Interface defining the structure of organization settings for recipe visibility
 */
interface OrganizationSettings {
    recipeDetailsToDisplayPublicly: {
        category: boolean;
        ingredients: boolean;
        nutritionalInformation: boolean;
        allergenInformation: boolean;
        preparationSteps: boolean;
        totalTime: boolean;
        yieldPortioning: boolean;
        cost: boolean;
        dietarySuitability: boolean;
        cuisineType: boolean;
        media: boolean;
        links: boolean;
        scale: boolean;
        serveIn: boolean;
        garnish: boolean;
        haccp: boolean;
    };
    publicRecipeCallToAction: {
        contactForm: boolean;
        contactInfo: {
            enabled: boolean;
            name: string;
            phone: string;
            email: string;
            link: string;
        };
        customCtaLink: {
            enabled: boolean;
            text: string;
            link: string;
        };
        none: boolean;
    };
    publicRecipeSettings: {
        publicStoreAccess: boolean;
    };
}

/**
 * Helper function to filter recipe data based on organization settings
 * @param recipeData The complete recipe data object
 * @param organizationSettings The organization settings that control field visibility
 * @returns Filtered recipe data containing only allowed fields based on organization settings
 */
export function filterPublicRecipeFields(recipeData: any, organizationSettings?: OrganizationSettings): any {
    if (!recipeData) return null;

    // Default settings if not provided
    const settings = organizationSettings?.recipeDetailsToDisplayPublicly || {
        category: true,
        ingredients: true,
        nutritionalInformation: true,
        allergenInformation: true,
        preparationSteps: true,
        totalTime: false,
        yieldPortioning: false,
        cost: false,
        dietarySuitability: false,
        cuisineType: false,
        media: false,
        links: false,
        scale: false,
        serveIn: false,
        garnish: false,
        haccp: false,
    };

    // Map settings to recipe fields
    const allowedFields = new Set([
        // Always allowed basic fields
        'id',
        'recipe_title',
        'recipe_public_title',
        'recipe_description',
        'recipe_slug',
        'recipe_status',
        'has_recipe_public_visibility',
        'recipe_impression',
        'created_at',
        'updated_at',
        'organization_settings', // Always include organization settings

        // Conditionally allowed fields based on settings
        ...(settings.category ? ['categories'] : []),
        ...(settings.ingredients ? ['ingredients'] : []),
        ...(settings.nutritionalInformation ? [
            'nutrition_attributes',
            'vitamin_a',
            'vitamin_c',
            'calcium',
            'iron'
        ] : []),
        ...(settings.allergenInformation ? ['allergen_attributes'] : []),
        ...(settings.preparationSteps ? ['steps'] : []),
        ...(settings.totalTime ? [
            'recipe_preparation_time',
            'recipe_cook_time'
        ] : []),
        ...(settings.yieldPortioning ? [
            'recipe_yield',
            'recipe_yield_unit',
            'recipe_total_portions',
            'recipe_single_portion_size'
        ] : []),
        ...(settings.cost ? [
            'ingredient_costs',
            'total_cost',
            'cost_per_portion'
        ] : []),
        ...(settings.dietarySuitability ? ['dietary_attributes'] : []),
        ...(settings.cuisineType ? ['cuisine_attributes'] : []),
        // Always include recipe_placeholder if media is enabled
        ...(settings.media ? ['recipe_placeholder'] : []),
        // Always include resources field, but we'll filter the content based on media/links settings
        ...((settings.media || settings.links) ? ['resources'] : []),
        ...(settings.serveIn ? ['recipe_serve_in'] : []),
        ...(settings.garnish ? ['recipe_garnish'] : []),
        ...(settings.haccp ? ['haccp_attributes'] : [])
    ]);

    // Helper function to check if a field or its parent is allowed
    const isFieldAllowed = (field: string): boolean => {
        return Array.from(allowedFields).some(allowedField =>
            field === allowedField ||
            field.startsWith(`${allowedField}.`) ||
            allowedField.startsWith(`${field}.`)
        );
    };

    // Recursive function to filter nested objects
    const filterObject = (obj: any, parentKey: string = ''): any => {
        if (!obj || typeof obj !== 'object') return obj;

        if (Array.isArray(obj)) {
            // Special handling for resources array
            if (parentKey === 'resources') {
                return obj.filter(resource => {
                    // Filter resources based on type and organization settings
                    if (resource.type === 'item' && !settings.media) {
                        return false; // Hide media files if media is disabled
                    }
                    if (resource.type === 'link' && !settings.links) {
                        return false; // Hide links if links are disabled
                    }
                    return true; // Include resource if its type is allowed
                }).map(item => filterObject(item, parentKey));
            }
            return obj.map(item => filterObject(item, parentKey));
        }

        const filtered: any = {};
        for (const [key, value] of Object.entries(obj)) {
            const fullKey = parentKey ? `${parentKey}.${key}` : key;

            if (isFieldAllowed(fullKey)) {
                filtered[key] = filterObject(value, fullKey);
            }
        }
        return filtered;
    };

    // Filter the main recipe data
    return filterObject(recipeData);
}
